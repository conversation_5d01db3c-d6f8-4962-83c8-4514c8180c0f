import Homey from 'homey';
import PairSession from 'homey/lib/PairSession';

class RouterDriver extends Homey.Driver {
  private pairingSessions: Map<string, any> = new Map();

  /**
   * onInit is called when the driver is initialized.
   */
  async onInit() {
    this.log('Router driver has been initialized');
  }

  /**
   * Simple network discovery to find potential routers
   */
  private async discoverRouters(): Promise<any[]> {
    this.log('Starting router discovery');

    const potentialRouters = [
      { ip: '*************', name: 'Ruijie X32-PRO Router (*************)' },
      { ip: '***********', name: '<PERSON><PERSON><PERSON><PERSON> X32-PRO Router (***********)' },
      { ip: '***********', name: '<PERSON>uijie X32-PRO Router (***********)' },
    ];

    const discoveredRouters = [];

    for (const router of potentialRouters) {
      try {
        this.log(`Testing connection to ${router.ip}`);

        // Try to connect to the router's web interface
        const axios = require('axios');
        const response = await axios.get(`http://${router.ip}/cgi-bin/luci/`, {
          timeout: 3000,
          validateStatus: () => true // Accept any status code
        });

        if (response.status === 200 && response.data.includes('X32-PRO')) {
          this.log(`Found Ruijie X32-PRO router at ${router.ip}`);
          discoveredRouters.push({
            name: router.name,
            data: {
              id: `router-${router.ip.replace(/\./g, '-')}`,
            },
            settings: {
              ip: router.ip,
              username: 'admin',
              password: '', // User will need to enter this
            },
          });
        }
      } catch (error) {
        this.log(`No router found at ${router.ip}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    this.log(`Discovery completed. Found ${discoveredRouters.length} routers`);
    return discoveredRouters;
  }

  /**
   * onPairListDevices is called when a user is adding a device and the 'list_devices' view is called.
   * This should return an array with the data of devices that are available for pairing.
   */
  async onPairListDevices() {
    this.log('onPairListDevices called');

    // Check all pairing sessions for pending devices
    for (const [sessionId, pendingDevice] of this.pairingSessions.entries()) {
      if (pendingDevice) {
        this.log(`Returning stored device data for session ${sessionId}:`, JSON.stringify(pendingDevice, null, 2));
        return [pendingDevice];
      }
    }

    // If no pending device, try to discover routers on the network
    this.log('No pending device found, attempting network discovery');
    try {
      const discoveredRouters = await this.discoverRouters();
      if (discoveredRouters.length > 0) {
        this.log(`Network discovery found ${discoveredRouters.length} routers:`, JSON.stringify(discoveredRouters, null, 2));
        return discoveredRouters;
      } else {
        this.log('Network discovery completed but no routers found');
      }
    } catch (error) {
      this.error('Network discovery failed:', error);
    }

    this.log('No routers available for pairing, returning empty array');
    return [];
  }

  /**
   * Handle the pairing process
   */
  async onPair(session: PairSession): Promise<void> {
    this.log('onPair called, setting up pairing session');

    // Generate a unique session ID for this pairing session
    const sessionId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
    this.log(`Created pairing session with ID: ${sessionId}`);

    // Register listener for when the user submits router credentials
    session.setHandler('validate_router_credentials', async (data) => {
      try {
        const { ip, password } = data;
        const username = 'admin'; // Default username for Ruijie routers

        this.log(`Validating router credentials - IP: ${ip}, Password: ${password ? '******' : 'not set'}`);

        // Try to connect to the router to validate credentials
        this.log('Importing RouterApiClient');
        const { RouterApiClient } = await import('../../lib/RouterApiClient');

        this.log('Creating new RouterApiClient instance');
        const client = new RouterApiClient(ip, username, password);

        this.log('Initializing client to test connection');
        await client.init();

        // If we get here, the connection was successful
        this.log('Router connection validated successfully');

        // Create device data for the list_devices view
        const deviceId = `router-${ip.replace(/\./g, '-')}`;
        const deviceData = {
          name: `Ruijie X32-PRO Router (${ip})`,
          data: {
            id: deviceId,
          },
          settings: {
            ip,
            username,
            password,
          },
        };

        // Store device data for the list_devices view
        this.pairingSessions.set(sessionId, deviceData);
        this.log(`Stored device data for session ${sessionId}:`, deviceData);

        // Clean up the client
        client.stopConnectionCheck();

        return true;
      } catch (error) {
        this.error('Failed to validate router credentials:', error);
        return false;
      }
    });

    // Clean up session when pairing is done or cancelled
    session.setHandler('disconnect', async () => {
      this.log(`Cleaning up pairing session ${sessionId}`);
      this.pairingSessions.delete(sessionId);
    });

    // Note: We don't need to set a 'list_devices' handler here because we have onPairListDevices() method
    // The onPairListDevices() method will be called automatically by Homey for the list_devices template
  }
}

module.exports = RouterDriver;
